import { SuiService } from '../sui/sui.service';
import { AuthService } from '../auth/auth.service';
export interface AccessControlRule {
    conditionType: 'email' | 'wallet' | 'time' | 'hybrid';
    allowedEmails?: string[];
    allowedAddresses?: string[];
    allowedSuiNS?: string[];
    accessStartTime?: number;
    accessEndTime?: number;
    maxAccessDuration?: number;
    requireAllConditions?: boolean;
    maxAccessCount?: number;
}
export interface CreateAccessControlRequest {
    fileCid: string;
    accessRule: AccessControlRule;
}
export interface UpdateAccessControlRequest {
    fileCid: string;
    accessRule: AccessControlRule;
}
export interface ValidateAccessRequest {
    fileCid: string;
    userAddress: string;
    userEmail?: string;
}
export interface AccessControlResponse {
    success: boolean;
    message: string;
    transactionDigest?: string;
    accessGranted?: boolean;
    accessControlId?: string;
}
export interface AccessControlInfo {
    fileCid: string;
    owner: string;
    conditionType: string;
    allowedEmails: string[];
    allowedAddresses: string[];
    accessStartTime?: number;
    accessEndTime?: number;
    requireAllConditions: boolean;
    currentAccessCount: number;
    totalUserRecords: number;
}
export declare class AccessControlService {
    private readonly suiService;
    private readonly authService;
    private readonly logger;
    constructor(suiService: SuiService, authService: AuthService);
    createAccessControl(token: string, request: CreateAccessControlRequest): Promise<AccessControlResponse>;
    updateAccessControl(token: string, request: UpdateAccessControlRequest): Promise<AccessControlResponse>;
    validateAccess(token: string, request: ValidateAccessRequest): Promise<AccessControlResponse>;
    getAccessControlInfo(token: string, fileCid: string): Promise<{
        success: boolean;
        data?: AccessControlInfo;
        message: string;
    }>;
    private validateAccessRule;
    private isValidEmail;
    private isValidSuiAddress;
    private normalizeSuiAddress;
    resolveEmailToAddress(email: string): Promise<string | null>;
    generateShareLink(token: string, request: {
        fileCid: string;
        expirationTime?: number;
        maxUses?: number;
    }): Promise<{
        success: boolean;
        data?: any;
        message: string;
    }>;
    generateShareLinkTest(request: {
        fileCid: string;
        expirationTime?: number;
        maxUses?: number;
    }): Promise<{
        success: boolean;
        data?: any;
        message: string;
    }>;
    validateShareLink(shareId: string, token?: string): Promise<{
        success: boolean;
        data?: any;
        message: string;
    }>;
    processBulkUpload(token: string, file: Express.Multer.File, fileCid: string, conditionType: 'email' | 'wallet' | 'hybrid'): Promise<{
        success: boolean;
        data?: any;
        message: string;
    }>;
    private parseBulkData;
    private isValidSuiNS;
}
